import { useGetMyCourtBookingList } from "@/api/my-booking-service";
import React from "react";
import MyCourtBookingListItem from "./my-court-booking-list-item";

interface CourtBookingSectionProps {
  timeFilter: "upcoming" | "past";
}

const MyCourtBookingSection: React.FC<CourtBookingSectionProps> = ({ timeFilter }) => {
  const { myCourtBookingList } = useGetMyCourtBookingList({ page: 1, limit: 10 });

  return (
    <div>
      <div className="mb-4 text-sm text-gray-600">Showing {timeFilter} bookings</div>
      {/* <pre>{JSON.stringify(myCourtBookingList, null, 2)}</pre> */}
      <MyCourtBookingListItem />
    </div>
  );
};

export default MyCourtBookingSection;
