import React from "react";

const MyCourtBookingListItem = () => {
  return (
    <div className="relative h-[182px] w-[403px]">
      <div className="absolute top-0 left-0 h-[182px] w-[403px] rounded-[15px] border border-[#efefef] bg-white" />
      <div className="absolute top-[108.30px] left-[293px] h-[26.54px] w-[84px] justify-start text-[15px] leading-[25px] font-normal text-white">
        Get Direction
      </div>
      <div data-svg-wrapper className="absolute top-[55.21px] left-[18px]">
        <svg
          width="372"
          height="2"
          viewBox="0 0 372 2"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M371 1.21292L1 1.21289"
            stroke="#E3E3E3"
            stroke-linecap="round"
            stroke-dasharray="2 4"
          />
        </svg>
      </div>
      <div className="absolute top-[11.68px] left-[18px] h-[21.23px] w-[99px] justify-start">
        <span className="text-[15px] leading-tight font-normal text-[#c3c3c3]">Booking</span>
        <span className="text-[15px] leading-tight font-normal text-black"> </span>
        <span className="text-[15px] leading-tight font-medium text-black">#2312</span>
      </div>
      <div data-svg-wrapper className="absolute top-[20.17px] left-[359px]">
        <svg
          width="26"
          height="16"
          viewBox="0 0 26 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect y="0.171875" width="26" height="14.8642" rx="5" fill="#F5F5F5" />
        </svg>
      </div>
      <div data-svg-wrapper className="absolute top-[26.54px] left-[366px]">
        <svg width="3" height="4" viewBox="0 0 3 4" fill="none" xmlns="http://www.w3.org/2000/svg">
          <ellipse cx="1.5" cy="2.13556" rx="1.5" ry="1.59259" fill="black" />
        </svg>
      </div>
      <div data-svg-wrapper className="absolute top-[26.54px] left-[371px]">
        <svg width="3" height="4" viewBox="0 0 3 4" fill="none" xmlns="http://www.w3.org/2000/svg">
          <ellipse cx="1.5" cy="2.13556" rx="1.5" ry="1.59259" fill="black" />
        </svg>
      </div>
      <div data-svg-wrapper className="absolute top-[26.54px] left-[376px]">
        <svg width="3" height="4" viewBox="0 0 3 4" fill="none" xmlns="http://www.w3.org/2000/svg">
          <ellipse cx="1.5" cy="2.13556" rx="1.5" ry="1.59259" fill="black" />
        </svg>
      </div>
      <div className="absolute top-[61px] left-[19px] h-[71px] w-40 justify-start text-xl leading-[25px] font-medium text-black">
        Tyson court 1<br />
        Tuesday 24 Apr
        <br />
        1:00 PM - 2:00 PM
      </div>
      <div className="absolute top-[148px] left-[19px] inline-flex w-[107px] items-center justify-between rounded-[121px] bg-[#f8ffd6] px-2.5 outline outline-1 outline-offset-[-1px] outline-[#c0e702]">
        <div className="justify-start text-[15px] font-normal text-[#1c5534]">Court Booking</div>
      </div>
    </div>
  );
};

export default MyCourtBookingListItem;
